#!/usr/bin/env python3
"""
Comprehensive Coinbase PEM Fix using proper Fernet decryption
"""
import os
import sys
import base64

# Add project root to path
sys.path.append('.')

def setup_fernet_key():
    """Setup the Fernet key from .fernet_key file"""
    try:
        fernet_key_path = '.fernet_key'
        if os.path.exists(fernet_key_path):
            with open(fernet_key_path, 'r') as f:
                content = f.read().strip()
                if content.startswith('FERNET_KEY='):
                    key = content.split('=', 1)[1]
                    os.environ['FERNET_KEY'] = key
                    print(f"[SUCCESS] [SETUP] Fernet key loaded from {fernet_key_path}")
                    return True
                else:
                    print(f"[ERROR] [SETUP] Invalid format in {fernet_key_path}")
                    return False
        else:
            print(f"[ERROR] [SETUP] {fernet_key_path} not found")
            return False
    except Exception as e:
        print(f"[ERROR] [SETUP] Failed to load Fernet key: {e}")
        return False

def main():
    print("[MAIN] Coinbase PEM Fix with Proper Decryption")
    print("=" * 60)

    # Step 1: Setup Fernet key
    if not setup_fernet_key():
        print("[ERROR] [MAIN] Cannot proceed without Fernet key")
        return False

    # Step 2: Get encrypted key from environment
    encrypted_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    if not encrypted_key:
        print("[ERROR] [MAIN] No ENCRYPTED_COINBASE_PRIVATE_KEY found")
        return False

    print(f"[SUCCESS] [MAIN] Found encrypted key ({len(encrypted_key)} chars)")
    
    # Step 3: Decrypt using secure_credentials
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value

        print("[DECRYPT] Attempting decryption...")
        decrypted_key = decrypt_value(encrypted_key)

        if decrypted_key is None:
            print("[ERROR] [DECRYPT] Decryption returned None")
            return False

        if decrypted_key == encrypted_key:
            print("[ERROR] [DECRYPT] Decryption returned unchanged data")
            return False

        print(f"[SUCCESS] [DECRYPT] Successfully decrypted! Length: {len(decrypted_key)}")

    except Exception as e:
        print(f"[ERROR] [DECRYPT] Decryption failed: {e}")
        return False

    # Step 4: Analyze the decrypted content
    print(f"\n[ANALYZE] Content Analysis:")
    print(f"  - Length: {len(decrypted_key)} characters")
    print(f"  - First 100 chars: {repr(decrypted_key[:100])}")
    print(f"  - Last 100 chars: {repr(decrypted_key[-100:])}")

    # Check PEM structure
    has_begin = '-----BEGIN' in decrypted_key
    has_end = '-----END' in decrypted_key
    print(f"  - Has PEM header: {'[SUCCESS]' if has_begin else '[ERROR]'}")
    print(f"  - Has PEM footer: {'[SUCCESS]' if has_end else '[ERROR]'}")

    # Check for invalid characters
    invalid_chars = []
    for i, char in enumerate(decrypted_key):
        byte_val = ord(char)
        if byte_val < 32 and byte_val not in [10, 13]:  # Allow \n and \r
            invalid_chars.append((i, byte_val, repr(char)))

    if invalid_chars:
        print(f"  - Invalid characters: [ERROR] Found {len(invalid_chars)}")
        for pos, val, char in invalid_chars[:5]:  # Show first 5
            print(f"    Position {pos}: byte {val} {char}")
    else:
        print(f"  - Invalid characters: [SUCCESS] None found")
    
    # Step 5: Validate with cryptography library
    try:
        print(f"\n🧪 [VALIDATE] Testing with cryptography library...")
        from cryptography.hazmat.primitives import serialization
        
        private_key = serialization.load_pem_private_key(
            decrypted_key.encode('utf-8'),
            password=None
        )
        
        print(f"🎉 [VALIDATE] PEM is VALID! Key type: {type(private_key).__name__}")
        
        # Test key properties
        if hasattr(private_key, 'key_size'):
            print(f"✅ [VALIDATE] Key size: {private_key.key_size} bits")
        if hasattr(private_key, 'curve'):
            print(f"✅ [VALIDATE] Curve: {private_key.curve.name}")
        
        # Key is valid, save it for reference
        with open('valid_coinbase_private_key.pem', 'w') as f:
            f.write(decrypted_key)
        print("💾 [SAVE] Valid key saved to 'valid_coinbase_private_key.pem'")
        
        print(f"\n🎉 [SUCCESS] Coinbase private key is VALID!")
        print(f"🔍 [INFO] The key should work for JWT token generation")
        print(f"💡 [SOLUTION] The system should be able to authenticate properly now")
        
        return True
        
    except Exception as e:
        print(f"❌ [VALIDATE] PEM validation failed: {e}")
        
        # Try to fix common issues
        print(f"\n🔧 [FIX] Attempting to fix PEM format...")
        
        fixed_key = decrypted_key
        
        # Remove invalid bytes
        if invalid_chars:
            print("  - Removing invalid bytes...")
            fixed_key = ''.join(char for char in fixed_key if ord(char) >= 32 or char in ['\n', '\r'])
        
        # Fix escaped newlines
        if '\\n' in fixed_key:
            print("  - Fixing escaped newlines...")
            fixed_key = fixed_key.replace('\\n', '\n')
        
        # Normalize line endings
        fixed_key = fixed_key.replace('\r\n', '\n').replace('\r', '\n')
        
        # Ensure proper PEM format
        import re
        
        # Add proper spacing around headers
        fixed_key = re.sub(r'-----BEGIN([^-]+)-----\s*', r'-----BEGIN\1-----\n', fixed_key)
        fixed_key = re.sub(r'\s*-----END([^-]+)-----', r'\n-----END\1-----', fixed_key)
        
        # Remove extra blank lines
        fixed_key = re.sub(r'\n\n+', '\n', fixed_key)
        
        # Ensure final newline
        fixed_key = fixed_key.strip() + '\n'
        
        # Test the fixed version
        try:
            print(f"🧪 [FIX] Testing fixed version...")
            private_key = serialization.load_pem_private_key(
                fixed_key.encode('utf-8'),
                password=None
            )
            
            print(f"🎉 [FIX] Fixed PEM is VALID! Key type: {type(private_key).__name__}")
            
            # Save the fixed version
            with open('fixed_coinbase_private_key.pem', 'w') as f:
                f.write(fixed_key)
            print("💾 [SAVE] Fixed key saved to 'fixed_coinbase_private_key.pem'")
            
            # Re-encrypt the fixed key
            try:
                print("🔐 [RE-ENCRYPT] Re-encrypting fixed key...")
                from src.utils.cryptography.secure_credentials import encrypt_value
                
                new_encrypted = encrypt_value(fixed_key)
                print(f"✅ [RE-ENCRYPT] Re-encryption successful!")
                print(f"📋 [SOLUTION] Replace in .env file:")
                print(f"ENCRYPTED_COINBASE_PRIVATE_KEY={new_encrypted}")
                
                # Save the update command
                with open('env_update_command.txt', 'w') as f:
                    f.write(f"ENCRYPTED_COINBASE_PRIVATE_KEY={new_encrypted}\n")
                print("📄 [SAVE] Update command saved to 'env_update_command.txt'")
                
                return True
                
            except Exception as re_encrypt_error:
                print(f"⚠️ [RE-ENCRYPT] Re-encryption failed: {re_encrypt_error}")
                print("💡 [SOLUTION] Use the fixed .pem file manually")
                return True
                
        except Exception as fix_error:
            print(f"❌ [FIX] Even fixed version failed: {fix_error}")
            
            # Save debug information
            with open('coinbase_key_fix_debug.txt', 'w') as f:
                f.write(f"Decryption successful: Yes\n")
                f.write(f"Original decrypted length: {len(decrypted_key)}\n")
                f.write(f"Fixed length: {len(fixed_key)}\n")
                f.write(f"Invalid chars found: {len(invalid_chars)}\n\n")
                f.write("Original decrypted content:\n")
                f.write(repr(decrypted_key))
                f.write("\n\nFixed content:\n")
                f.write(repr(fixed_key))
                f.write("\n\nFixed readable:\n")
                f.write(fixed_key)
            
            print("💾 [DEBUG] Debug info saved to 'coinbase_key_fix_debug.txt'")
            return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 FINAL SUCCESS: Coinbase PEM key is valid and ready to use!")
            print("🚀 The system should now be able to authenticate with Coinbase")
        else:
            print("\n❌ FINAL FAILURE: Could not fix the PEM key")
            print("💡 Check the debug files for more information")
    except Exception as e:
        print(f"\n💥 FATAL ERROR: {e}")
        import traceback
        traceback.print_exc()
