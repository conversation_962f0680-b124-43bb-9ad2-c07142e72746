#!/usr/bin/env python3
"""
SAFE Analysis of ENCRYPTED_COINBASE_PRIVATE_KEY
This script ONLY examines the encrypted value - NO MODIFICATIONS
"""
import os
import sys
import base64
from dotenv import load_dotenv

# Load environment safely
load_dotenv()

def analyze_encrypted_value_safely():
    """Safely analyze the encrypted value format without modifications"""
    print("[SAFE-ANALYSIS] Examining ENCRYPTED_COINBASE_PRIVATE_KEY format")

    encrypted_value = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    if not encrypted_value:
        print("  [ERROR] No ENCRYPTED_COINBASE_PRIVATE_KEY found")
        return

    print(f"  [INFO] Value length: {len(encrypted_value)} characters")
    print(f"  [INFO] First 50 chars: {encrypted_value[:50]}")
    print(f"  [INFO] Last 50 chars: {encrypted_value[-50:]}")
    print(f"  [INFO] Starts with 'gAAAAA': {encrypted_value.startswith('gAAAAA')}")

    # Check if it's valid base64
    try:
        decoded = base64.urlsafe_b64decode(encrypted_value + '==')  # Add padding
        print(f"  [SUCCESS] Valid base64: {len(decoded)} bytes when decoded")
    except Exception as e:
        print(f"  [ERROR] Invalid base64: {e}")

    # Check if it's valid Fernet token format
    try:
        if encrypted_value.startswith('gAAAAA'):
            # Fernet tokens have specific structure: version + timestamp + iv + ciphertext + tag
            decoded = base64.urlsafe_b64decode(encrypted_value)
            if len(decoded) >= 57:  # Minimum Fernet token size
                version = decoded[0]
                timestamp = decoded[1:9]
                print(f"  [INFO] Fernet version: {version}")
                print(f"  [INFO] Token structure appears valid")
            else:
                print(f"  [ERROR] Token too short for valid Fernet: {len(decoded)} bytes")
    except Exception as e:
        print(f"  [ERROR] Error analyzing Fernet structure: {e}")

def check_environment_safely():
    """Safely check all encryption-related environment variables"""
    print("\n[SAFE-CHECK] Current encryption environment")

    vars_to_check = [
        'ENCRYPTED_COINBASE_PRIVATE_KEY',
        'ENCRYPTED_FERNET_KEY',
        'FERNET_KEY',
        'CREDENTIALS_PASSWORD',
        'COINBASE_API_KEY_NAME',
        'COINBASE_ORG_ID'
    ]

    for var in vars_to_check:
        value = os.getenv(var)
        if value:
            print(f"  [SUCCESS] {var}: {len(value)} chars, starts with '{value[:10]}'")
        else:
            print(f"  [ERROR] {var}: Not set")

def check_key_files_safely():
    """Safely check what key files exist"""
    print("\n[SAFE-CHECK] Available key files")

    key_files = [
        '.fernet_key',
        'src/utils/cryptography/fernet.key',
        'src/utils/cryptography/private.pem',
        'src/utils/cryptography/public.pem'
    ]

    for file_path in key_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                print(f"  [SUCCESS] {file_path}: {len(content)} chars, starts with '{content[:20]}'")
            except:
                print(f"  [SUCCESS] {file_path}: Exists but binary/unreadable")
        else:
            print(f"  [ERROR] {file_path}: Not found")

if __name__ == "__main__":
    print("[SAFE-MODE] Coinbase encryption analysis - READ ONLY")
    print("=" * 60)

    analyze_encrypted_value_safely()
    check_environment_safely()
    check_key_files_safely()

    print("\n[SUCCESS] [SAFE-MODE] Analysis complete - NO CHANGES MADE")
